import 'package:crud/controllers/counter_controller.dart';
import 'package:crud/controllers/initial_controller.dart';
import 'package:refreshed/refreshed.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';

class HomeView extends StatelessWidget {
  HomeView({super.key});

  final controller = Get.put(CounterController());
  final initialController = Get.find<InitialController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      child: SafeArea(
        child: LayoutBuilder(
          builder:
              (context, constraints) => GestureDetector(
                onTap: () => FocusScope.of(context).unfocus(),
                child: SingleChildScrollView(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: constraints.maxHeight,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24.0),
                      child: Column(
                        spacing: 16,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Obx(
                            () => Center(
                              child: Text('Hello World! - ${controller.count}'),
                            ),
                          ),
                          Align(
                            alignment:
                                Alignment
                                    .center, // use Alignment.centerLeft to left-align
                            child: ConstrainedBox(
                              constraints: const BoxConstraints(maxWidth: 200),
                              child: PrimaryButton(
                                onPressed: () => controller.increment(),
                                trailing: const Icon(Icons.add),
                                child: const Text('Add'),
                              ),
                            ),
                          ),
                          IconButton.secondary(
                            onPressed: () => initialController.changeTheme(),
                            density: ButtonDensity.icon,
                            icon: const Icon(Icons.light_mode),
                          ),
                          const TextField(
                            placeholder: Text('Enter your email'),
                            features: [
                              InputFeature.clear(
                                visibility: InputFeatureVisibility.textNotEmpty,
                              ),
                            ],
                          ),
                          const TextField(
                            placeholder: Text('Enter your password'),
                            features: [
                              InputFeature.clear(
                                visibility: InputFeatureVisibility.textNotEmpty,
                              ),
                              InputFeature.passwordToggle(
                                mode: PasswordPeekMode.toggle,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
        ),
      ),
    );
  }
}
